<?php
    session_start();
?>

<html>
    <head>
        <title>Lab 9</title>
    </head>

    <body>
        <h1>Thank you for your payment</h1>
        
        <?php
            error_reporting(E_ERROR | E_PARSE);

            if ($_SESSION["buy"] != null)
            {
                echo "Totally you want to buy ".count($_SESSION["buy"])." items. And they are: ";
                echo "<hr>";
                echo "<ul>";
                foreach ($_SESSION["buy"] as $item)
                    echo "<li>".$item[3]." &times; ".$item[0]."</li>";
                echo "</ul>";
                echo "<hr>";

                //echo "Please pay $".$_SESSION["totalCost"]."<br>";
            }

            session_unset();
            session_destroy();

            echo "Your transaction is done. Please click <a href='6-26.php'>here</a> to buy more! <br>";
        ?>
    </body>
</html>
